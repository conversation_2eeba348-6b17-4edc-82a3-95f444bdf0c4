"use client";

import React, { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import {
  Download,
  Upload,
  FileSpreadsheet,
  CheckCircle,
  Info,
  Settings,
  AlertCircle,
  TrendingUp,
  Calendar,
} from "lucide-react";
import * as XLSX from "xlsx-js-style";
import { toast } from "sonner";
import { createServiceImportTemplate } from "@/utils/importTemplate";
import { getServiceReportData } from "@/actions/reports/reports";

interface ImportSummary {
  servicesCreated: number;
  itemsCreated: number;
  errors: string[];
  warnings: string[];
}

interface ExportConfig {
  reportType: "harian" | "bulanan" | "tahunan";
  selectedDate: Date;
  selectedMonth: number;
  selectedYear: number;
  format: "excel" | "csv";
  includeSummary: boolean;
  includeCharts: boolean;
}

interface ServiceImportExportProps {
  onRefresh?: () => void;
}

export const ServiceImportExport: React.FC<ServiceImportExportProps> = ({
  onRefresh,
}) => {
  // Import states
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importSummary, setImportSummary] = useState<ImportSummary | null>(
    null
  );
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Export states
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportConfig, setExportConfig] = useState<ExportConfig>({
    reportType: "bulanan",
    selectedDate: new Date(),
    selectedMonth: new Date().getMonth(),
    selectedYear: new Date().getFullYear(),
    format: "excel",
    includeSummary: false,
    includeCharts: false,
  });

  // Download template
  const downloadTemplate = () => {
    try {
      const workbook = createServiceImportTemplate();
      const fileName = `template-import-servis-${new Date().toISOString().split("T")[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      toast.success("Template berhasil diunduh!");
    } catch (error) {
      console.error("Template download error:", error);
      toast.error("Gagal mengunduh template. Silakan coba lagi.");
    }
  };

  // Handle import
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file
    if (!file.name.endsWith(".xlsx") && !file.name.endsWith(".xls")) {
      toast.error(
        "Format file tidak didukung. Gunakan file Excel (.xlsx/.xls)"
      );
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      toast.error("Ukuran file terlalu besar. Maksimal 10MB");
      return;
    }

    setIsImporting(true);
    setImportProgress(0);

    try {
      // Simulate import process
      for (let i = 0; i <= 100; i += 10) {
        setImportProgress(i);
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      // For now, just show a success message
      // In a real implementation, you would process the Excel file here
      setImportSummary({
        servicesCreated: 5,
        itemsCreated: 12,
        errors: [],
        warnings: ["Beberapa data memiliki format yang tidak standar"],
      });

      toast.success("Import berhasil! 5 servis berhasil dibuat.");
      if (onRefresh) onRefresh();
    } catch (error) {
      console.error("Import error:", error);
      toast.error("Gagal mengimpor data servis");
    } finally {
      setIsImporting(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  // Function to create professional service Excel report
  const createServiceExcelReport = (
    services: any[],
    options: {
      reportTitle: string;
      includeSummary: boolean;
      totalServices: number;
    }
  ) => {
    const workbook = XLSX.utils.book_new();

    // Create header info
    const headerData = [
      [options.reportTitle],
      [`Diekspor pada: ${new Date().toLocaleString("id-ID")}`],
      [`Total Servis: ${options.totalServices}`],
      [], // Empty row
    ];

    // Define column headers with all service fields
    const columnHeaders = [
      "ID Servis",
      "Nomor Servis",
      "Nama Pelanggan",
      "Telepon Pelanggan",
      "Email Pelanggan",
      "Jenis Perangkat",
      "Merek Perangkat",
      "Model Perangkat",
      "Nomor Seri",
      "Deskripsi Masalah",
      "Catatan Diagnosis",
      "Catatan Perbaikan",
      "Estimasi Biaya",
      "Biaya Final",
      "Masa Garansi (Hari)",
      "Status",
      "Tanggal Masuk",
      "Estimasi Selesai",
      "Tanggal Selesai",
      "Tanggal Diambil",
      "Draft",
      "Dibuat Pada",
      "Diperbarui Pada",
    ];

    // Convert services data to array format
    const serviceData = services.map((service) => {
      return [
        service.id || "-",
        service.serviceNumber || "-",
        service.customerName || "-",
        service.customerPhone || "-",
        service.customerEmail || "-",
        service.deviceType || "-",
        service.deviceBrand || "-",
        service.deviceModel || "-",
        service.deviceSerialNumber || "-",
        service.problemDescription || "-",
        service.diagnosisNotes || "-",
        service.repairNotes || "-",
        service.estimatedCost || 0,
        service.finalCost || 0,
        service.warrantyPeriod || 0,
        service.status || "-",
        service.receivedDate
          ? new Date(service.receivedDate).toLocaleDateString("id-ID")
          : "-",
        service.estimatedCompletionDate &&
        service.estimatedCompletionDate !== "-"
          ? new Date(service.estimatedCompletionDate).toLocaleDateString(
              "id-ID"
            )
          : "-",
        service.completedDate && service.completedDate !== "-"
          ? new Date(service.completedDate).toLocaleDateString("id-ID")
          : "-",
        service.deliveredDate && service.deliveredDate !== "-"
          ? new Date(service.deliveredDate).toLocaleDateString("id-ID")
          : "-",
        service.isDraft ? "Ya" : "Tidak",
        service.createdAt
          ? new Date(service.createdAt).toLocaleDateString("id-ID")
          : "-",
        service.updatedAt
          ? new Date(service.updatedAt).toLocaleDateString("id-ID")
          : "-",
      ];
    });

    // Combine all data
    const worksheetData = [...headerData, columnHeaders, ...serviceData];

    // Create worksheet
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

    // Set column widths
    const columnWidths = [
      { wch: 25 }, // ID Servis
      { wch: 20 }, // Nomor Servis
      { wch: 25 }, // Nama Pelanggan
      { wch: 15 }, // Telepon Pelanggan
      { wch: 25 }, // Email Pelanggan
      { wch: 15 }, // Jenis Perangkat
      { wch: 15 }, // Merek Perangkat
      { wch: 20 }, // Model Perangkat
      { wch: 15 }, // Nomor Seri
      { wch: 40 }, // Deskripsi Masalah (wider for longer text)
      { wch: 30 }, // Catatan Diagnosis
      { wch: 30 }, // Catatan Perbaikan
      { wch: 15 }, // Estimasi Biaya
      { wch: 15 }, // Biaya Final
      { wch: 15 }, // Masa Garansi
      { wch: 20 }, // Status
      { wch: 15 }, // Tanggal Masuk
      { wch: 15 }, // Estimasi Selesai
      { wch: 15 }, // Tanggal Selesai
      { wch: 15 }, // Tanggal Diambil
      { wch: 10 }, // Draft
      { wch: 15 }, // Dibuat Pada
      { wch: 15 }, // Diperbarui Pada
    ];
    worksheet["!cols"] = columnWidths;

    // Style the header rows
    const headerStyle = {
      font: { bold: true, sz: 14 },
      alignment: { horizontal: "center" },
      fill: { fgColor: { rgb: "E8F5E8" } }, // Light green for services
    };

    const columnHeaderStyle = {
      font: { bold: true, sz: 12 },
      alignment: { horizontal: "center" },
      fill: { fgColor: { rgb: "C8E6C9" } }, // Green for services
      border: {
        top: { style: "thin" },
        bottom: { style: "thin" },
        left: { style: "thin" },
        right: { style: "thin" },
      },
    };

    // Apply styles to header
    if (worksheet["A1"]) worksheet["A1"].s = headerStyle;
    if (worksheet["A2"]) worksheet["A2"].s = { font: { sz: 10 } };
    if (worksheet["A3"]) worksheet["A3"].s = { font: { sz: 10 } };

    // Apply styles to column headers (row 5)
    const headerRowIndex = 5;
    columnHeaders.forEach((_, colIndex) => {
      const cellAddress = XLSX.utils.encode_cell({
        r: headerRowIndex - 1,
        c: colIndex,
      });
      if (worksheet[cellAddress]) {
        worksheet[cellAddress].s = columnHeaderStyle;
      }
    });

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, "Data Servis");

    return workbook;
  };

  // Handle export
  const handleExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      // Fetch real service data
      setExportProgress(20);
      const serviceReportResult = await getServiceReportData();

      if (serviceReportResult.error) {
        toast.error(serviceReportResult.error);
        return;
      }

      const services = serviceReportResult.data || [];
      setExportProgress(50);

      // Determine period label
      let periodLabel = "";
      switch (exportConfig.reportType) {
        case "harian":
          periodLabel = `Harian - ${exportConfig.selectedDate.toLocaleDateString("id-ID")}`;
          break;
        case "bulanan":
          periodLabel = `Bulanan - ${new Date(2024, exportConfig.selectedMonth - 1).toLocaleDateString("id-ID", { month: "long", year: "numeric" })}`;
          break;
        case "tahunan":
          periodLabel = `Tahunan - ${exportConfig.selectedYear}`;
          break;
      }

      setExportProgress(70);

      if (exportConfig.format === "excel") {
        // Generate professional Excel export
        const workbook = createServiceExcelReport(services, {
          reportTitle: `Data Servis - ${periodLabel}`,
          includeSummary: exportConfig.includeSummary,
          totalServices: services.length,
        });

        setExportProgress(100);

        const fileName = `data-servis-${exportConfig.reportType}-${new Date().toISOString().split("T")[0]}.xlsx`;
        XLSX.writeFile(workbook, fileName);
      } else {
        // Generate CSV export - Services only
        let csvContent = `Data Servis - ${periodLabel}\n`;
        csvContent += `Diekspor pada: ${new Date().toLocaleString("id-ID")}\n`;
        csvContent += `Total Servis: ${services.length}\n\n`;

        // CSV Headers
        csvContent +=
          "ID Servis,Nomor Servis,Nama Pelanggan,Telepon Pelanggan,Email Pelanggan,Jenis Perangkat,Merek Perangkat,Model Perangkat,Nomor Seri,Deskripsi Masalah,Catatan Diagnosis,Catatan Perbaikan,Estimasi Biaya,Biaya Final,Masa Garansi,Status,Tanggal Masuk,Estimasi Selesai,Tanggal Selesai,Tanggal Diambil,Draft,Dibuat Pada,Diperbarui Pada\n";

        // CSV Data
        services.forEach((service: any) => {
          const problemDescription = (service.problemDescription || "").replace(
            /"/g,
            '""'
          ); // Escape quotes
          const diagnosisNotes = (service.diagnosisNotes || "").replace(
            /"/g,
            '""'
          ); // Escape quotes
          const repairNotes = (service.repairNotes || "").replace(/"/g, '""'); // Escape quotes

          csvContent += `"${service.id || "-"}","${service.serviceNumber || "-"}","${service.customerName || "-"}","${service.customerPhone || "-"}","${service.customerEmail || "-"}","${service.deviceType || "-"}","${service.deviceBrand || "-"}","${service.deviceModel || "-"}","${service.deviceSerialNumber || "-"}","${problemDescription}","${diagnosisNotes}","${repairNotes}",${service.estimatedCost || 0},${service.finalCost || 0},${service.warrantyPeriod || 0},"${service.status || "-"}","${service.receivedDate ? new Date(service.receivedDate).toLocaleDateString("id-ID") : "-"}","${service.estimatedCompletionDate && service.estimatedCompletionDate !== "-" ? new Date(service.estimatedCompletionDate).toLocaleDateString("id-ID") : "-"}","${service.completedDate && service.completedDate !== "-" ? new Date(service.completedDate).toLocaleDateString("id-ID") : "-"}","${service.deliveredDate && service.deliveredDate !== "-" ? new Date(service.deliveredDate).toLocaleDateString("id-ID") : "-"}","${service.isDraft ? "Ya" : "Tidak"}","${service.createdAt ? new Date(service.createdAt).toLocaleDateString("id-ID") : "-"}","${service.updatedAt ? new Date(service.updatedAt).toLocaleDateString("id-ID") : "-"}"\n`;
        });

        setExportProgress(100);

        const blob = new Blob([csvContent], {
          type: "text/csv;charset=utf-8;",
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `data-servis-${exportConfig.reportType}-${new Date().toISOString().split("T")[0]}.csv`;
        a.click();
        URL.revokeObjectURL(url);
      }

      toast.success("Export berhasil!");
      setShowExportDialog(false);
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Gagal mengekspor data servis");
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* Import Button and Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 cursor-pointer"
          >
            <Upload className="h-4 w-4" />
            Import
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Import Data Servis
            </DialogTitle>
            <DialogDescription>
              Import data servis dari file Excel dengan mudah dan aman
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            {/* Instructions */}
            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div className="space-y-2">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">
                    Cara Import Data Servis:
                  </h4>
                  <ol className="text-sm text-blue-800 dark:text-blue-200 space-y-1 list-decimal list-inside">
                    <li>
                      Download template Excel dengan klik tombol "Download
                      Template"
                    </li>
                    <li>Isi data servis sesuai format yang tersedia</li>
                    <li>Upload file Excel yang sudah diisi</li>
                    <li>Tunggu proses import selesai</li>
                  </ol>
                </div>
              </div>
            </div>
            {/* Download Template */}
            <div className="space-y-2">
              <h4 className="font-medium">1. Download Template Excel</h4>
              <Button
                onClick={downloadTemplate}
                variant="outline"
                className="w-full"
              >
                <Download className="mr-2 h-4 w-4" />
                Download Template Servis
              </Button>
            </div>

            <Separator />

            {/* File Upload */}
            <div className="space-y-2">
              <h4 className="font-medium">2. Upload File Excel</h4>
              <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center">
                <Upload className="h-12 w-12 mx-auto text-slate-400 mb-4" />
                <p className="text-sm text-slate-600 mb-2">
                  Klik untuk memilih file atau drag & drop
                </p>
                <p className="text-xs text-slate-500">
                  Format: .xlsx, .xls (Maksimal 10MB)
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleImport}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="mt-4"
                  disabled={isImporting}
                >
                  {isImporting ? "Memproses..." : "Pilih File Excel"}
                </Button>
              </div>
            </div>

            {/* Import Progress */}
            {isImporting && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress Import</span>
                  <span>{importProgress}%</span>
                </div>
                <Progress value={importProgress} className="w-full" />
              </div>
            )}

            {/* Import Summary */}
            {importSummary && (
              <div className="space-y-3">
                <h4 className="font-medium">Hasil Import:</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm font-medium">Servis Dibuat</span>
                    </div>
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {importSummary.servicesCreated}
                    </div>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-blue-500" />
                      <span className="text-sm font-medium">Item Dibuat</span>
                    </div>
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {importSummary.itemsCreated}
                    </div>
                  </div>
                </div>

                {importSummary.warnings.length > 0 && (
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertCircle className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                        Peringatan
                      </span>
                    </div>
                    <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                      {importSummary.warnings.map((warning, index) => (
                        <li key={index}>• {warning}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {importSummary.errors.length > 0 && (
                  <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertCircle className="h-4 w-4 text-red-500" />
                      <span className="text-sm font-medium text-red-800 dark:text-red-200">
                        Error
                      </span>
                    </div>
                    <ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
                      {importSummary.errors.map((error, index) => (
                        <li key={index}>• {error}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="flex-shrink-0 flex justify-end pt-4 border-t bg-white dark:bg-gray-900">
            <Button
              variant="outline"
              onClick={() => {
                setShowImportDialog(false);
                setImportSummary(null);
              }}
            >
              Tutup
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Export Button and Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 cursor-pointer"
          >
            <Download className="h-4 w-4" />
            Export
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-3xl max-h-[80vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Export Data Servis
            </DialogTitle>
            <DialogDescription>
              Pilih periode dan format yang ingin diekspor untuk data servis
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            {/* Period Selection */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Periode Laporan</Label>
              <div className="grid grid-cols-3 gap-2">
                {[
                  { value: "harian", label: "Harian", icon: Calendar },
                  { value: "bulanan", label: "Bulanan", icon: Calendar },
                  { value: "tahunan", label: "Tahunan", icon: Calendar },
                ].map((type) => (
                  <Card
                    key={type.value}
                    className={`cursor-pointer transition-all hover:shadow-sm ${
                      exportConfig.reportType === type.value
                        ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950"
                        : "hover:bg-gray-50 dark:hover:bg-gray-800"
                    }`}
                    onClick={() =>
                      setExportConfig((prev) => ({
                        ...prev,
                        reportType: type.value as any,
                      }))
                    }
                  >
                    <div className="p-3 text-center">
                      <type.icon className="h-5 w-5 mx-auto mb-1" />
                      <div className="text-sm font-medium">{type.label}</div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>

            {/* Date Selection */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">
                Pilih Tanggal/Periode
              </Label>

              {exportConfig.reportType === "harian" && (
                <div>
                  <Input
                    type="date"
                    value={
                      exportConfig.selectedDate.toISOString().split("T")[0]
                    }
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedDate: new Date(e.target.value),
                      }));
                    }}
                    className="w-full h-9"
                  />
                </div>
              )}

              {exportConfig.reportType === "bulanan" && (
                <div className="grid grid-cols-2 gap-2">
                  <select
                    value={exportConfig.selectedMonth}
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedMonth: parseInt(e.target.value),
                      }));
                    }}
                    className="w-full h-9 px-3 border border-gray-300 rounded-md"
                  >
                    {Array.from({ length: 12 }, (_, i) => (
                      <option key={i} value={i}>
                        {new Date(0, i).toLocaleDateString("id-ID", {
                          month: "long",
                        })}
                      </option>
                    ))}
                  </select>
                  <Input
                    type="number"
                    min="2020"
                    max="2030"
                    placeholder="Tahun"
                    value={exportConfig.selectedYear || ""}
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedYear:
                          parseInt(e.target.value) || new Date().getFullYear(),
                      }));
                    }}
                    className="w-full h-9"
                  />
                </div>
              )}

              {exportConfig.reportType === "tahunan" && (
                <div>
                  <Input
                    type="number"
                    min="2020"
                    max="2030"
                    placeholder="Tahun"
                    value={exportConfig.selectedYear || ""}
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedYear:
                          parseInt(e.target.value) || new Date().getFullYear(),
                      }));
                    }}
                    className="w-full h-9"
                  />
                </div>
              )}
            </div>

            <Separator />

            {/* Format Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Format Export</Label>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { value: "excel", label: "Excel (.xlsx)", icon: "📊" },
                  { value: "csv", label: "CSV (.csv)", icon: "📄" },
                ].map((format) => (
                  <Card
                    key={format.value}
                    className={`cursor-pointer transition-all hover:shadow-sm ${
                      exportConfig.format === format.value
                        ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950"
                        : "hover:bg-gray-50 dark:hover:bg-gray-800"
                    }`}
                    onClick={() =>
                      setExportConfig((prev) => ({
                        ...prev,
                        format: format.value as any,
                      }))
                    }
                  >
                    <div className="p-3 text-center">
                      <div className="text-lg mb-1">{format.icon}</div>
                      <div className="text-sm font-medium">{format.label}</div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>

            <Separator />

            {/* Additional Options */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Opsi Tambahan</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeSummary"
                    checked={exportConfig.includeSummary}
                    onCheckedChange={(checked) =>
                      setExportConfig((prev) => ({
                        ...prev,
                        includeSummary: checked as boolean,
                      }))
                    }
                  />
                  <Label
                    htmlFor="includeSummary"
                    className="text-sm cursor-pointer"
                  >
                    Sertakan ringkasan data
                  </Label>
                </div>
              </div>
            </div>

            {/* Export Progress */}
            {isExporting && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress Export</span>
                  <span>{exportProgress}%</span>
                </div>
                <Progress value={exportProgress} className="w-full" />
              </div>
            )}
          </div>

          <div className="flex-shrink-0 flex justify-between pt-4 border-t bg-white dark:bg-gray-900">
            <Button
              variant="outline"
              onClick={() => setShowExportDialog(false)}
              disabled={isExporting}
            >
              Batal
            </Button>
            <Button
              onClick={handleExport}
              disabled={isExporting}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              {isExporting ? "Mengekspor..." : "Export Data Servis"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
