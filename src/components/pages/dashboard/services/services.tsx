"use client";

import React, { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import DashboardLayout from "@/components/layout/dashboardlayout";
import {
  Clock,
  Wrench,
  Truck,
  CheckCircle,
  ChevronUp,
  ChevronDown,
  ArrowUpDownIcon,
} from "lucide-react";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { Pagination } from "@/components/ui/pagination";

// Import types and components
import {
  Service,
  ServiceCounts,
  ColumnVisibility,
  ServiceStatus,
} from "./types";
import { getDefaultServiceColumnVisibility } from "./config/columnConfig";
import { ServiceStatusCards } from "./components/ServiceStatusCards";
import { ServiceActions } from "./components/ServiceActions";
import { ServiceTableDesktop } from "./components/ServiceTableDesktop";
import { ServiceFilterState } from "./components/ServiceFilter";

interface ServicesPageProps {
  services: Service[];
  serviceCounts: ServiceCounts;
}

const ServicesPage: React.FC<ServicesPageProps> = (props) => {
  const { services = [], serviceCounts } = props;
  const searchParams = useSearchParams();
  const [searchTerm, setSearchTerm] = useState("");
  const [subTab, setSubTab] = useState(() => {
    // Check if there's a tab parameter in the URL
    const tabParam = searchParams.get("tab");
    return tabParam === "drafts" ? "drafts" : "all-services";
  });
  const [filteredServices, setFilteredServices] = useState<Service[]>(services);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [paginatedServices, setPaginatedServices] = useState<Service[]>([]);

  // Column visibility state with localStorage persistence
  const [columnVisibility, setColumnVisibility] = useState<ColumnVisibility>(
    () => {
      // Try to get saved column visibility from localStorage
      if (typeof window !== "undefined") {
        try {
          const savedVisibility = localStorage.getItem(
            "serviceColumnVisibility"
          );
          if (savedVisibility) {
            const parsed = JSON.parse(savedVisibility) as ColumnVisibility;

            // Validate that all required keys exist in the saved data
            const defaultVisibility = getDefaultServiceColumnVisibility();
            const hasAllKeys = Object.keys(defaultVisibility).every(
              (key) => key in parsed
            );

            if (hasAllKeys) {
              console.log(
                "Using saved service column visibility from localStorage:",
                parsed
              );
              return parsed;
            } else {
              // If saved data is incomplete, remove it and use defaults
              localStorage.removeItem("serviceColumnVisibility");
              console.warn(
                "Incomplete service column visibility data found, using defaults"
              );
            }
          }
        } catch (error) {
          console.error(
            "Failed to parse saved service column visibility:",
            error
          );
          // Clear corrupted data
          localStorage.removeItem("serviceColumnVisibility");
        }
      }

      // Default column visibility for new users or when localStorage is invalid
      const defaultVisibility = getDefaultServiceColumnVisibility();
      console.log(
        "Using default service column visibility for new user:",
        defaultVisibility
      );
      return defaultVisibility;
    }
  );

  // Sorting state
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

  // Filter state
  const [filters, setFilters] = useState<ServiceFilterState>({});

  // Refresh function for after import
  const handleRefresh = () => {
    // In a real implementation, this would refetch data from the server
    // For now, we'll just show a success message
    toast.success("Data berhasil diperbarui!");
  };

  // Function to handle column sorting
  const handleSort = (field: string) => {
    if (sortField === field) {
      // If already sorting by this field, toggle direction
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // If sorting by a new field, set it and default to ascending
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Function to get sort icon
  const getSortIcon = (field: string) => {
    if (sortField !== field) {
      return <ArrowUpDownIcon className="h-4 w-4 ml-1 opacity-50" />;
    }
    return sortDirection === "asc" ? (
      <ChevronUp className="h-4 w-4 ml-1" />
    ) : (
      <ChevronDown className="h-4 w-4 ml-1" />
    );
  };

  // Filter and sort services based on search term and sort settings
  useEffect(() => {
    let result = [...services];

    // Reset to first page when filters change
    setCurrentPage(1);

    // Apply sub-tab filter first
    if (subTab === "drafts") {
      result = result.filter((service) => service.isDraft === true);
    } else if (subTab === "all-services") {
      result = result.filter((service) => service.isDraft === false);
    }

    // Apply search filter
    if (searchTerm) {
      result = result.filter(
        (service) =>
          service.serviceNumber
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          service.customerName
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          service.deviceBrand
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          service.deviceModel.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply advanced filters
    if (filters.status) {
      result = result.filter((service) => service.status === filters.status);
    }

    if (filters.deviceType) {
      result = result.filter(
        (service) => service.deviceType === filters.deviceType
      );
    }

    if (filters.customerName) {
      result = result.filter((service) =>
        service.customerName
          .toLowerCase()
          .includes(filters.customerName!.toLowerCase())
      );
    }

    if (filters.serviceNumber) {
      result = result.filter((service) =>
        service.serviceNumber
          .toLowerCase()
          .includes(filters.serviceNumber!.toLowerCase())
      );
    }

    if (filters.estimatedCostRange) {
      const { min, max } = filters.estimatedCostRange;
      result = result.filter((service) => {
        const cost = service.estimatedCost || 0;
        if (min !== undefined && cost < min) return false;
        if (max !== undefined && cost > max) return false;
        return true;
      });
    }

    if (filters.isDraft !== undefined) {
      result = result.filter((service) => service.isDraft === filters.isDraft);
    }

    // Apply sorting if a sort field is selected
    if (sortField) {
      result.sort((a, b) => {
        let valueA, valueB;

        // Handle different field types
        switch (sortField) {
          case "serviceNumber":
            valueA = a.serviceNumber.toLowerCase();
            valueB = b.serviceNumber.toLowerCase();
            break;
          case "customerName":
            valueA = a.customerName.toLowerCase();
            valueB = b.customerName.toLowerCase();
            break;
          case "customerPhone":
            valueA = a.customerPhone.toLowerCase();
            valueB = b.customerPhone.toLowerCase();
            break;
          case "deviceType":
            valueA = a.deviceType.toLowerCase();
            valueB = b.deviceType.toLowerCase();
            break;
          case "deviceBrand":
            valueA = a.deviceBrand.toLowerCase();
            valueB = b.deviceBrand.toLowerCase();
            break;
          case "deviceModel":
            valueA = a.deviceModel.toLowerCase();
            valueB = b.deviceModel.toLowerCase();
            break;
          case "deviceSerialNumber":
            valueA = (a.deviceSerialNumber || "").toLowerCase();
            valueB = (b.deviceSerialNumber || "").toLowerCase();
            break;
          case "status":
            valueA = a.status;
            valueB = b.status;
            break;
          case "receivedDate":
            valueA = new Date(a.receivedDate).getTime();
            valueB = new Date(b.receivedDate).getTime();
            break;
          case "estimatedCompletionDate":
            valueA = a.estimatedCompletionDate
              ? new Date(a.estimatedCompletionDate).getTime()
              : 0;
            valueB = b.estimatedCompletionDate
              ? new Date(b.estimatedCompletionDate).getTime()
              : 0;
            break;
          case "estimatedCost":
            valueA = a.estimatedCost || 0;
            valueB = b.estimatedCost || 0;
            break;
          case "warrantyPeriod":
            valueA = a.warrantyPeriod || 0;
            valueB = b.warrantyPeriod || 0;
            break;
          default:
            valueA = a[sortField as keyof Service] || "";
            valueB = b[sortField as keyof Service] || "";
        }

        // Compare based on direction
        if (sortDirection === "asc") {
          return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;
        } else {
          return valueA < valueB ? 1 : valueA > valueB ? -1 : 0;
        }
      });
    }

    setFilteredServices(result);
  }, [services, searchTerm, sortField, sortDirection, subTab, filters]);

  // Apply pagination to filtered services
  useEffect(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    setPaginatedServices(filteredServices.slice(startIndex, endIndex));
  }, [filteredServices, currentPage, itemsPerPage]);

  // Save column visibility to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem(
        "serviceColumnVisibility",
        JSON.stringify(columnVisibility)
      );
    }
  }, [columnVisibility]);

  // Function to get status badge
  const getStatusBadge = (status: ServiceStatus) => {
    switch (status) {
      case ServiceStatus.DITERIMA:
        return (
          <Badge variant="default" className="bg-blue-500 whitespace-nowrap">
            <Clock className="h-3 w-3 mr-1" />
            Diterima
          </Badge>
        );
      case ServiceStatus.PROSES_MENUNGGU_SPAREPART:
        return (
          <Badge variant="default" className="bg-amber-500 whitespace-nowrap">
            <Wrench className="h-3 w-3 mr-1" />
            Proses/Menunggu Sparepart
          </Badge>
        );
      case ServiceStatus.SELESAI_BELUM_DIAMBIL:
        return (
          <Badge variant="default" className="bg-purple-500 whitespace-nowrap">
            <Truck className="h-3 w-3 mr-1" />
            Selesai & Belum Diambil
          </Badge>
        );
      case ServiceStatus.SELESAI_SUDAH_DIAMBIL:
        return (
          <Badge variant="default" className="bg-green-500 whitespace-nowrap">
            <CheckCircle className="h-3 w-3 mr-1" />
            Selesai & Sudah Diambil
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="whitespace-nowrap">
            {status}
          </Badge>
        );
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Service Status Summary Cards */}
        <ServiceStatusCards serviceCounts={serviceCounts} />

        {/* Sub Tabs */}
        <Tabs
          defaultValue="all-services"
          value={subTab}
          onValueChange={setSubTab}
          className="w-full"
        >
          <TabsList className="mb-4 w-full md:w-fit">
            <TabsTrigger value="all-services">Daftar Servis</TabsTrigger>
            <TabsTrigger value="drafts">Draf Servis</TabsTrigger>
          </TabsList>

          <TabsContent value="all-services" className="space-y-6">
            {/* Header Actions */}
            <ServiceActions
              columnVisibility={columnVisibility}
              setColumnVisibility={setColumnVisibility}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              filters={filters}
              onFilterChange={setFilters}
              onRefresh={handleRefresh}
            />

            {/* Services List */}
            <div className="overflow-x-auto">
              {/* Table View */}
              <ServiceTableDesktop
                services={paginatedServices}
                columnVisibility={columnVisibility}
                handleSort={handleSort}
                getSortIcon={getSortIcon}
                getStatusBadge={getStatusBadge}
                searchTerm={searchTerm}
              />
            </div>

            {/* Pagination */}
            <div className="mt-4">
              <Pagination
                currentPage={currentPage}
                totalPages={Math.ceil(filteredServices.length / itemsPerPage)}
                onPageChange={setCurrentPage}
                itemsPerPage={itemsPerPage}
                onItemsPerPageChange={setItemsPerPage}
                totalItems={filteredServices.length}
              />
            </div>
          </TabsContent>

          <TabsContent value="drafts" className="space-y-6">
            {/* Header Actions */}
            <ServiceActions
              columnVisibility={columnVisibility}
              setColumnVisibility={setColumnVisibility}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              filters={filters}
              onFilterChange={setFilters}
              onRefresh={handleRefresh}
            />

            {/* Services List */}
            <div className="overflow-x-auto">
              {/* Table View */}
              <ServiceTableDesktop
                services={paginatedServices}
                columnVisibility={columnVisibility}
                handleSort={handleSort}
                getSortIcon={getSortIcon}
                getStatusBadge={getStatusBadge}
                searchTerm={searchTerm}
              />
            </div>

            {/* Pagination */}
            <div className="mt-4">
              <Pagination
                currentPage={currentPage}
                totalPages={Math.ceil(filteredServices.length / itemsPerPage)}
                onPageChange={setCurrentPage}
                itemsPerPage={itemsPerPage}
                onItemsPerPageChange={setItemsPerPage}
                totalItems={filteredServices.length}
              />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default ServicesPage;
