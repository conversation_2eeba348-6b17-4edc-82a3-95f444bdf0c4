import { z } from "zod";
import { PurchaseSchema, PurchaseItemSchema } from "@/schemas/zod";

// Extend the PurchaseItemSchema with additional fields
export const EnhancedPurchaseItemSchema = PurchaseItemSchema.extend({
  unit: z.string().optional().default("Buah"),
  tax: z.string().optional(),
  discountPercentage: z.number().optional().default(0),
  discountAmount: z.number().optional().default(0),
});

// Extend the base PurchaseSchema with additional fields for the enhanced UI
export const EnhancedPurchaseSchema = PurchaseSchema.extend({
  // Use the enhanced item schema
  items: z
    .array(EnhancedPurchaseItemSchema)
    .min(1, { message: "Minimal satu produk harus dipilih" }),
  // Additional fields for the enhanced UI
  deliveryDate: z.date().optional(),
  paymentStatus: z.enum(["paid", "pending", "partial"]).default("paid"),
  paymentDueDate: z.date().optional(),
  lampiran: z
    .array(
      z.object({
        url: z.string(),
        filename: z.string(),
      })
    )
    .optional()
    .default([]), // Array of file objects with URL and original filename
  memo: z.string().optional(),
  trackDelivery: z.boolean().default(false),
  notifyOnArrival: z.boolean().default(false),
  isDraft: z.boolean().default(false),
  // Tax settings
  priceIncludesTax: z.boolean().default(false),
  // Global discount fields
  globalDiscountPercentage: z.number().optional().default(0),
  globalDiscountAmount: z.number().optional().default(0),
  // New fields
  supplierEmail: z
    .string()
    .min(1, { message: "Email supplier wajib diisi" })
    .email({ message: "Email tidak valid" }),
  supplierPhone: z.string().optional(),
  transactionDate: z.date().default(() => new Date()),
  transactionNumber: z.string().optional(),
  warehouseId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  billingAddress: z.string().optional(),
});

// Define the type for the form values
export type PurchaseFormValues = z.infer<typeof EnhancedPurchaseSchema>;

// Define the type for products and suppliers
export interface Product {
  id: string;
  name: string;
  cost: number | null;
  unit?: string;
  image?: string | null;
  createdAt?: string | Date; // Can be ISO date string or Date object
}

export interface Supplier {
  id: string;
  name: string;
  email: string | null;
  phone: string | null;
  address: string | null; // Added address property
}
